<?php
namespace Aws\PersonalizeEvents;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Personalize Events** service.
 * @method \Aws\Result putEvents(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putEventsAsync(array $args = [])
 * @method \Aws\Result putItems(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putItemsAsync(array $args = [])
 * @method \Aws\Result putUsers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putUsersAsync(array $args = [])
 */
class PersonalizeEventsClient extends AwsClient {}
