/*
Copyright (c) 2003-2022, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image2', 'tr', {
	alt: '<PERSON>ernatif Yazı',
	btnUpload: '<PERSON><PERSON><PERSON><PERSON>',
	captioned: 'Başlıklı resim',
	captionPlaceholder: '<PERSON><PERSON><PERSON><PERSON>k',
	infoTab: 'Resim Bilgisi',
	lockRatio: 'Oran<PERSON> Kilitle',
	menu: 'Resi<PERSON> Özellikleri',
	pathName: 'Resim',
	pathNameCaption: 'başlık',
	resetSize: 'Boyutu Başa Döndür',
	resizer: 'Boyutlandırmak için, tıklayın ve sürükleyin',
	title: '<PERSON>si<PERSON> Ö<PERSON>likler<PERSON>',
	uploadTab: '<PERSON><PERSON><PERSON><PERSON><PERSON> Yükle',
	urlMissing: '<PERSON><PERSON><PERSON> URL kaynağı bulunamadı.',
	altMissing: 'Alternatif yazı eksik.'
} );
