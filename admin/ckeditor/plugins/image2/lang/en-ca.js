/*
Copyright (c) 2003-2022, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image2', 'en-ca', {
	alt: 'Alternative Text',
	btnUpload: 'Send it to the Server',
	captioned: 'Captioned image', // MISSING
	captionPlaceholder: 'Caption', // MISSING
	infoTab: 'Image Info',
	lockRatio: 'Lock Ratio',
	menu: 'Image Properties',
	pathName: 'image', // MISSING
	pathNameCaption: 'caption', // MISSING
	resetSize: 'Reset Size',
	resizer: 'Click and drag to resize', // MISSING
	title: 'Image Properties',
	uploadTab: 'Upload',
	urlMissing: 'Image source URL is missing.', // MISSING
	altMissing: 'Alternative text is missing.' // MISSING
} );
