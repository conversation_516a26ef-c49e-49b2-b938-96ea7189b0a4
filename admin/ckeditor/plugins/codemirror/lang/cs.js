/*
Copyright (c) 2003-2012, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'codemirror', 'cs', {
    copyright: "Copyright CodeMirror © <PERSON><PERSON>. All rights reserved.",
	dlgTitle: 'About CodeMirror Source Plugin',
    moreInfoShortcuts: "Available Shortcuts",
    moreInfoShortcuts1: "<strong>CTRL + K</strong> to comment the currently selected text",
    moreInfoShortcuts2: "<strong>CTRL + SHIFT + K</strong> to uncomment currently selected text",
    moreInfoShortcuts3: "<strong>CTRL + ALT + K</strong> to auto format currently selected text",
    moreInfoShortcuts4: "<strong>CTRL + Q</strong> Expand / Collapse Code Block",
    moreInfoShortcuts5: "<strong>CTRL + F</strong> to perform a search",
    moreInfoShortcuts6: "<strong>CTRL + G</strong> to find next",
    moreInfoShortcuts7: "<strong>CTRL + SHIFT + G</strong> to find previous",
    moreInfoShortcuts8: "<strong>CTRL + SHIFT</strong> to find and replace",
    moreInfoShortcuts9: "<strong>CTRL + SHIFT + R</strong> to find and replace all",
	toolbar: 'Zdroj',
	searchCode: 'Prohledat zdroj',
	replaceCode: 'Replace Code (CTRL + SHIFT + F)',
	autoFormat: 'Formátovat výběr',
	commentSelectedRange: 'Zakomentovat výběr',
	uncommentSelectedRange: 'Odkomentovat výběr',
	autoCompleteToggle: 'Povolit/zakázat automatické doplňování HTML tagů'
});
