/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'ro', {
	find: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
	findOptions: 'Find Options',
	findWhat: 'G<PERSON><PERSON><PERSON><PERSON>:',
	matchCase: 'Deosebeşte majuscule de minuscule (Match case)',
	matchCyclic: 'Potrivește ciclic',
	matchWord: '<PERSON>ar cuvintele întregi',
	notFoundMsg: 'Textul specificat nu a fost găsit.',
	replace: 'Înlocuieşte',
	replaceAll: 'Înlocuieşte tot',
	replaceSuccessMsg: '%1 căutări înlocuite.',
	replaceWith: 'Înlocuieşte cu:',
	title: 'Găseş<PERSON> şi înlocuieşte'
} );
