/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'no', {
	find: '<PERSON>øk',
	findOptions: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
	findWhat: '<PERSON><PERSON><PERSON> etter:',
	matchCase: 'Skill mellom store og små bokstaver',
	matchCyclic: '<PERSON>øk i hele dokumentet',
	matchWord: 'Bare hele ord',
	notFoundMsg: 'Fant ikke søketeksten.',
	replace: 'Erstatt',
	replaceAll: 'Erstatt alle',
	replaceSuccessMsg: '%1 tilfelle(r) erstattet.',
	replaceWith: '<PERSON>rst<PERSON> med:',
	title: '<PERSON>ø<PERSON> og erstatt'
} );
