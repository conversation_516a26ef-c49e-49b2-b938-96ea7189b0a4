/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'si', {
	find: 'Find', // MISSING
	findOptions: 'Find Options', // MISSING
	findWhat: 'Find what:', // MISSING
	matchCase: 'Match case', // MISSING
	matchCyclic: 'Match cyclic', // MISSING
	matchWord: 'Match whole word', // MISSING
	notFoundMsg: 'The specified text was not found.', // MISSING
	replace: 'හිලව් කිරීම',
	replaceAll: 'සියල්ලම හිලව් කරන්න',
	replaceSuccessMsg: '%1 occurrence(s) replaced.', // MISSING
	replaceWith: 'Replace with:', // MISSING
	title: 'Find and Replace' // MISSING
} );
