/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'sl', {
	find: 'Najdi',
	findOptions: '<PERSON><PERSON><PERSON><PERSON> iskan<PERSON>',
	findWhat: 'Naj<PERSON>:',
	matchCase: '<PERSON><PERSON><PERSON><PERSON><PERSON> velike in male črke',
	matchCyclic: 'Primerjaj znake v cirilici',
	matchWord: 'Samo cele besede',
	notFoundMsg: 'Navedenega besedila nismo našli.',
	replace: 'Zamenjaj',
	replaceAll: 'Zamenjaj vse',
	replaceSuccessMsg: 'Zamenjali smo %1 pojavitev.',
	replaceWith: 'Zamenjaj z:',
	title: 'Najdi in zamenjaj'
} );
