/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'lv', {
	find: 'Mekl<PERSON>t',
	findOptions: '<PERSON>k<PERSON><PERSON>t uzstādīju<PERSON>',
	findWhat: 'Mekl<PERSON>t:',
	matchCase: 'Reģistrjūtīgs',
	matchCyclic: 'Sakrist cikliski',
	matchWord: '<PERSON>āsakr<PERSON>t pilnībā',
	notFoundMsg: 'Norādītā frāze netika atrasta.',
	replace: '<PERSON><PERSON><PERSON><PERSON>t',
	replaceAll: 'Aizvietot visu',
	replaceSuccessMsg: '%1 gadījums(i) aizvietoti',
	replaceWith: 'Nomainīt uz:',
	title: 'Meklēt un aizvietot'
} );
