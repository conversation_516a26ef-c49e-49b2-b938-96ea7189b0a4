﻿/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'link', 'de-ch', {
	acccessKey: 'Zugriffstaste',
	advanced: 'Erweitert',
	advisoryContentType: 'Inhaltstyp',
	advisoryTitle: 'Titel Beschreibung',
	anchor: {
		toolbar: 'Anker',
		menu: 'Anker bearbeiten',
		title: 'Ankereigenschaften',
		name: 'Ankername',
		errorName: 'Bitte geben Sie den Namen des An<PERSON> e<PERSON>',
		remove: 'An<PERSON> entfernen'
	},
	anchorId: 'Nach Elementkennung',
	anchorName: 'Nach Ankername',
	charset: 'Verknüpfter Ressourcenzeichensatz',
	cssClasses: 'Formatvorlagenklasse',
	download: 'Force Download', // MISSING
	displayText: 'Display Text', // MISSING
	emailAddress: 'E-Mail-Adresse',
	emailBody: 'Nachrichtentext',
	emailSubject: 'Betreffzeile',
	id: 'Kennung',
	info: 'Linkinfo',
	langCode: 'Sprachcode',
	langDir: 'Schreibrichtung',
	langDirLTR: 'Links nach Rechts (LTR)',
	langDirRTL: 'Rechts nach Links (RTL)',
	menu: 'Link bearbeiten',
	name: 'Name',
	noAnchors: '(Keine Anker im Dokument vorhanden)',
	noEmail: 'Bitte geben Sie E-Mail-Adresse an',
	noUrl: 'Bitte geben Sie die Link-URL an',
	other: '<andere>',
	popupDependent: 'Abhängig (Netscape)',
	popupFeatures: 'Pop-up Fenstereigenschaften',
	popupFullScreen: 'Vollbild (IE)',
	popupLeft: 'Linke Position',
	popupLocationBar: 'Adressleiste',
	popupMenuBar: 'Menüleiste',
	popupResizable: 'Grösse änderbar',
	popupScrollBars: 'Rollbalken',
	popupStatusBar: 'Statusleiste',
	popupToolbar: 'Werkzeugleiste',
	popupTop: 'Obere Position',
	rel: 'Beziehung',
	selectAnchor: 'Anker auswählen',
	styles: 'Style',
	tabIndex: 'Tab-Index',
	target: 'Zielseite',
	targetFrame: '<Frame>',
	targetFrameName: 'Ziel-Fenster-Name',
	targetPopup: '<Pop-up Fenster>',
	targetPopupName: 'Pop-up Fenster-Name',
	title: 'Link',
	toAnchor: 'Anker in dieser Seite',
	toEmail: 'E-Mail',
	toUrl: 'URL',
	toolbar: 'Link einfügen/editieren',
	type: 'Link-Typ',
	unlink: 'Link entfernen',
	upload: 'Hochladen'
} );
