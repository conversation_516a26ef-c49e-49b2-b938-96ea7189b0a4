/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'fr', {
	armenian: 'Numération arménienne',
	bulletedTitle: 'Propriétés de la liste à puces',
	circle: 'Cercle',
	decimal: 'Décimal (1, 2, 3, etc.)',
	decimalLeadingZero: 'Décimal précédé par un 0 (01, 02, 03, etc.)',
	disc: 'Disque',
	georgian: 'Numération géorgienne (an, ban, gan, etc.)',
	lowerAlpha: 'Lettres minuscules (a, b, c, d, e, etc.)',
	lowerGreek: 'Grec minuscule (alpha, bêta, gamma, etc.)',
	lowerRoman: 'Chiffres romains minuscules (i, ii, iii, iv, v, etc.)',
	none: 'Aucun',
	notset: '<indéfini>',
	numberedTitle: 'Propriétés de la liste numérotée',
	square: 'Carré',
	start: 'Début',
	type: 'Type',
	upperAlpha: 'Lettres majuscules (A, B, C, D, E, etc.)',
	upperRoman: 'Chiffres romains majuscules (I, II, III, IV, V, etc.)',
	validateStartNumber: 'Le premier élément de la liste doit être un nombre entier.'
} );
