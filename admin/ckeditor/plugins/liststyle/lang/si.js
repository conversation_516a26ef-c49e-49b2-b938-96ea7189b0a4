/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'si', {
	armenian: 'Armenian numbering', // MISSING
	bulletedTitle: 'Bulleted List Properties', // MISSING
	circle: 'Circle', // MISSING
	decimal: 'Decimal (1, 2, 3, etc.)', // MISSING
	decimalLeadingZero: 'Decimal leading zero (01, 02, 03, etc.)', // MISSING
	disc: 'Disc', // MISSING
	georgian: 'Georgian numbering (an, ban, gan, etc.)', // MISSING
	lowerAlpha: 'Lower Alpha (a, b, c, d, e, etc.)', // MISSING
	lowerGreek: 'Lower Greek (alpha, beta, gamma, etc.)', // MISSING
	lowerRoman: 'Lower Roman (i, ii, iii, iv, v, etc.)', // MISSING
	none: 'කිසිවක්ම නොවේ',
	notset: '<යොදා >',
	numberedTitle: 'Numbered List Properties', // MISSING
	square: 'Square', // MISSING
	start: 'Start', // MISSING
	type: 'වර්ගය',
	upperAlpha: 'Upper Alpha (A, B, C, D, E, etc.)', // MISSING
	upperRoman: 'Upper Roman (I, II, III, IV, V, etc.)', // MISSING
	validateStartNumber: 'List start number must be a whole number.' // MISSING
} );
