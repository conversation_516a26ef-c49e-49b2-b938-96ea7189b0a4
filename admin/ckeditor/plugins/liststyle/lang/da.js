/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'da', {
	armenian: 'Armensk nummering',
	bulletedTitle: 'Værdier for cirkelpunktopstilling',
	circle: 'Cirkel',
	decimal: 'Decimal (1, 2, 3, osv.)',
	decimalLeadingZero: 'Decimaler med 0 først (01, 02, 03, etc.)',
	disc: 'Værdier for diskpunktopstilling',
	georgian: 'Georgiansk nummering (an, ban, gan, etc.)',
	lowerAlpha: 'Små alfabet (a, b, c, d, e, etc.)',
	lowerGreek: 'Små græsk (alpha, beta, gamma, etc.)',
	lowerRoman: 'Små romerske (i, ii, iii, iv, v, etc.)',
	none: 'Ingen',
	notset: '<ikke defineret>',
	numberedTitle: '<PERSON>gens<PERSON>ber for nummereret liste',
	square: 'Firkant',
	start: 'Start',
	type: 'Type',
	upperAlpha: 'Store alfabet (A, B, C, D, E, etc.)',
	upperRoman: 'Store romerske (I, II, III, IV, V, etc.)',
	validateStartNumber: 'Den nummererede liste skal starte med et rundt nummer'
} );
