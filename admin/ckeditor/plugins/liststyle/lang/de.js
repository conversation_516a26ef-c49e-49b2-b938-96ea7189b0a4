/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'de', {
	armenian: 'Armenische Nummerierung',
	bulletedTitle: 'Aufzählungslisteneigenschaften',
	circle: 'Ring',
	decimal: 'Dezimal (1, 2, 3, etc.)',
	decimalLeadingZero: 'Dezimal mit führender Null (01, 02, 03, usw.)',
	disc: 'Kreis',
	georgian: 'Georgische Nummerierung (an, ban, gan, usw.)',
	lowerAlpha: 'Klein Alpha (a, b, c, d, e, usw.)',
	lowerGreek: 'Klein griechisch (alpha, beta, gamma, usw.)',
	lowerRoman: 'Klein römisch (i, ii, iii, iv, v, usw.)',
	none: 'Keine',
	notset: '<nicht festgelegt>',
	numberedTitle: 'Nummerierte Listeneigenschaften',
	square: 'Quadrat',
	start: 'Start',
	type: 'Typ',
	upperAlpha: 'Groß alpha (A, B, C, D, E, etc.)',
	upperRoman: '<PERSON><PERSON><PERSON> römisch (I, II, III, IV, V, usw.)',
	validateStartNumber: 'Listenstartnummer muss eine ganze Zahl sein.'
} );
