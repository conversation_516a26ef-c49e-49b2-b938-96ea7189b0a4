/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'hu', {
	armenian: '<PERSON><PERSON><PERSON><PERSON>',
	bulletedTitle: '<PERSON><PERSON><PERSON> lista tulajdonságai',
	circle: '<PERSON>ör',
	decimal: 'Arab számozás (1, 2, 3, stb.)',
	decimalLeadingZero: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON>et<PERSON> nullákkal (01, 02, 03, stb.)',
	disc: 'Korong',
	georgian: '<PERSON><PERSON><PERSON><PERSON>z<PERSON> (an, ban, gan, stb.)',
	lowerAlpha: 'Ki<PERSON><PERSON>űs (a, b, c, d, e, stb.)',
	lowerGreek: '<PERSON>örög (alpha, beta, gamma, stb.)',
	lowerRoman: '<PERSON><PERSON><PERSON><PERSON> kisbet<PERSON>s (i, ii, iii, iv, v, stb.)',
	none: 'Nincs',
	notset: '<Nincs beállítva>',
	numberedTitle: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lista tulajdonságai',
	square: 'Négy<PERSON>t',
	start: 'Kezdőszám',
	type: 'Típus',
	upperAlpha: 'Nagybetűs (A, B, C, D, E, stb.)',
	upperRoman: 'Római nagybetűs (I, II, III, IV, V, stb.)',
	validateStartNumber: 'A kezdőszám nem lehet tört érték.'
} );
