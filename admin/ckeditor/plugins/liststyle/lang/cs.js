/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'cs', {
	armenian: 'Arménské',
	bulletedTitle: '<PERSON>lastnosti odrážek',
	circle: '<PERSON><PERSON><PERSON><PERSON>',
	decimal: '<PERSON><PERSON><PERSON> č<PERSON>la (1, 2, 3, atd.)',
	decimalLeadingZero: 'Arabská čísla uvozená nulou (01, 02, 03, atd.)',
	disc: 'Kole<PERSON><PERSON>',
	georgian: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (an, ban, gan, atd.)',
	lowerAlpha: '<PERSON><PERSON> latinka (a, b, c, d, e, atd.)',
	lowerGreek: '<PERSON><PERSON> (alpha, beta, gamma, atd.)',
	lowerRoman: '<PERSON><PERSON> (i, ii, iii, iv, v, atd.)',
	none: 'Nic',
	notset: '<nenastaveno>',
	numberedTitle: 'Vlastnosti číslování',
	square: 'Čtverce',
	start: 'Počátek',
	type: 'Typ',
	upperAlpha: '<PERSON><PERSON><PERSON><PERSON> latinka (A, B, C, D, E, atd.)',
	upperRoman: 'Velké římské (I, II, III, IV, V, atd.)',
	validateStartNumber: 'Číslování musí začínat celým číslem.'
} );
