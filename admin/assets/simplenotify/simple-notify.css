.notifications-container{max-width:320px;width:100%;position:fixed;max-height:100vh;z-index:9999;pointer-events:none}.notifications-container.notify-is-x-center{left:50%;transform:translateX(-50%)}.notifications-container.notify-is-y-center{top:50%;transform:translateY(-50%)}.notifications-container.notify-is-center{left:50%;top:50%;transform:translate(-50%, -50%)}.notifications-container.notify-is-left{left:0}.notifications-container.notify-is-right{right:0}.notifications-container.notify-is-top{top:0}.notifications-container.notify-is-bottom{bottom:0}.notifications-container.notify-is-x-center.notify-is-top{top:var(--distance)}.notifications-container.notify-is-x-center.notify-is-bottom{bottom:var(--distance)}.notifications-container>*{pointer-events:auto}.notify{--notify-error: #eb5757;--notify-success: #6fcf97;--notify-warning: #f2c94c;--notify-gray: #333333;--notify-gray-2: #4d4d4d;--notify-gray-3: #828282;--notify-white: #fff;--notify-white-2: rgba(255, 255, 255, 0.8);--notify-padding: 0.75rem;--notify-icon-size: 32px;--notify-close-icon-size: 16px;font-family:-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;padding:var(--notify-padding);border-radius:6px;display:flex;align-items:center;width:100%;position:relative;user-select:none;position:relative;transition-timing-function:ease;box-sizing:border-box}.notify-is-left .notify{left:var(--distance)}.notify-is-right .notify{right:var(--distance)}.notify-is-top .notify,.notify-is-center .notify,.notify-is-y-center .notify,.notify-is-x-center.notify-is-top .notify{margin-top:var(--gap)}.notify-is-bottom .notify,.notify-is-x-center:not(.notify-is-top) .notify{margin-bottom:var(--gap)}.notify__icon{height:var(--notify-icon-size);width:var(--notify-icon-size);flex-shrink:0;display:flex;align-items:center;justify-content:center;margin-right:12px}.notify__close{position:absolute;right:12px;top:12px;cursor:pointer;height:var(--notify-close-icon-size);width:var(--notify-close-icon-size);display:flex;align-items:center;justify-content:center}.notify__close *{pointer-events:none}.notify__title{font-weight:600;font-size:1rem;padding-right:calc(var(--notify-padding) + var(--notify-close-icon-size))}.notify__text{font-size:0.875rem;margin-top:0.25rem}.notify--type-1{background-color:#fff;border:1px solid currentColor}.notify--type-1 .notify__close{color:var(--notify-gray-3)}.notify--type-1 .notify__title{color:var(--notify-gray)}.notify--type-1 .notify__text{color:var(--notify-gray-2)}.notify--type-2{color:var(--notify-gray)}.notify--type-3{color:var(--notify-white)}.notify--type-3 .notify__text{color:var(--notify-white-2)}.notify--error.notify--type-1{box-shadow:0 2px 26px rgba(215,0,0,0.1);color:var(--notify-error)}.notify--error.notify--type-2,.notify--error.notify--type-3{background-color:var(--notify-error)}.notify--warning.notify--type-1{box-shadow:0 2px 26px rgba(242,201,76,0.1);color:var(--notify-warning)}.notify--warning.notify--type-2,.notify--warning.notify--type-3{background-color:var(--notify-warning)}.notify--success.notify--type-1{box-shadow:0 2px 26px rgba(82,215,0,0.1);color:var(--notify-success)}.notify--success.notify--type-2,.notify--success.notify--type-3{background-color:var(--notify-success)}.notify--fade{will-change:opacity;opacity:0}.notify--fadeIn{opacity:1}.notify--slide{will-change:opacity, transform;opacity:0}.notify-is-center .notify--slide,.notify-is-y-center .notify--slide,.notify-is-x-center:not(.notify-is-bottom) .notify--slide{transform:translateY(-20px)}.notify-is-x-center.notify-is-bottom .notify--slide{transform:translateY(20px)}.notify-is-right .notify--slide{transform:translateX(calc(var(--distance) + 110%))}.notify-is-left .notify--slide{transform:translateX(calc((var(--distance) * -1) - 110%))}.notify-is-x-center:not(.notify-is-bottom) .notify--slideIn,.notify-is-center .notify--slideIn,.notify-is-y-center .notify--slideIn,.notify-is-x-center.notify-is-bottom .notify--slideIn{opacity:1;transform:translateY(0)}.notify-is-right .notify--slideIn,.notify-is-left .notify--slideIn{opacity:1;transform:translateX(0)}